<!DOCTYPE html>
<html id="html-root" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">تفاصيل المود - Modetaris</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🎮%3C/text%3E%3C/svg%3E">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        /* CSS مدمج - نفس التصميم الأصلي */
        :root {
            --bg-color: #1a1a1a;
            --header-color: #FFA500;
            --text-color: #ffffff;
            --button-color: #FFA500;
            --border-color: #333333;
            --accent-color: #FFD700;
            --card-color: #2D2D2D;
            --shadow-color: rgba(0,0,0,0.3);
            --font-family: 'Press Start 2P', monospace;
            --border-radius: 8px;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Header */
        header {
            background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            position: relative;
            padding: 1rem;
        }

        header h1 {
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            letter-spacing: 1px;
            text-align: center;
            font-size: 1.5rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 1rem;
            padding-bottom: 6rem;
        }

        /* Loading and Error States */
        .loading-screen {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--accent-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #ff4444;
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            margin: 20px;
        }

        /* Mod Container */
        .mod-container {
            background-color: var(--card-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            padding: 1rem;
        }

        .mod-header {
            background-color: var(--card-color);
            color: var(--text-color);
            padding: 15px;
            text-align: center;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
        }

        .mod-title {
            font-size: 1.25rem;
            margin: 0;
        }

        /* Image Display */
        .image-container {
            position: relative;
            width: 100%;
            padding-top: 56.25%; /* 16:9 aspect ratio */
            margin-bottom: 1rem;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .main-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: var(--border-radius);
        }

        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-item {
            background: var(--card-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            text-align: center;
        }

        .info-label {
            font-size: 12px;
            color: var(--accent-color);
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .info-value {
            font-size: 14px;
            line-height: 1.4;
        }

        /* Description */
        .description-container {
            background: var(--card-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        /* Download Button */
        .download-section {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem;
            background: var(--bg-color);
            border-top: 2px solid var(--border-color);
            display: flex;
            justify-content: center;
        }

        .download-button {
            background-color: var(--button-color);
            color: white;
            border: 2px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-width: 200px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .download-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
            background-color: #FF8C00;
        }

        .download-button.downloading {
            background-color: #4CAF50;
            border-color: #45a049;
            cursor: not-allowed;
        }

        .download-button.downloaded {
            background-color: #2196F3;
            border-color: #1976D2;
        }

        /* Utility Classes */
        .hidden { display: none !important; }
        .text-center { text-align: center; }
        .fade-in { animation: fadeIn 0.5s ease-in; }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container { padding: 0.5rem; }
            .download-button { min-width: 180px; font-size: 14px; }
            header h1 { font-size: 1.25rem; }
            .mod-title { font-size: 1rem; }
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            max-width: 300px;
            word-wrap: break-word;
            animation: slideInRight 0.3s ease-out;
        }

        .notification.success { background: #4CAF50; }
        .notification.error { background: #f44336; }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p style="color: white; margin-top: 1rem;">جاري تحميل بيانات المود...</p>
        </div>
    </div>

    <!-- Error Screen -->
    <div id="error-screen" class="loading-screen hidden">
        <div class="error-message" style="max-width: 400px;">
            <h2 style="margin-bottom: 1rem;">❌ خطأ في التحميل</h2>
            <p id="error-message-text">حدث خطأ أثناء تحميل بيانات المود</p>
            <button onclick="location.reload()" style="margin-top: 1rem; padding: 10px 20px; background: #fff; color: #f44336; border: none; border-radius: 4px; cursor: pointer;">
                🔄 إعادة المحاولة
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content" class="hidden">
        <!-- Header -->
        <header>
            <h1 id="site-name">Modetaris</h1>
        </header>

        <div class="container">
            <!-- Mod Title -->
            <div class="mod-header">
                <h1 id="mod-title" class="mod-title">جاري التحميل...</h1>
            </div>

            <!-- Main Image -->
            <div class="mod-container">
                <div class="image-container">
                    <img id="main-mod-image" class="main-image" src="" alt="صورة المود">
                </div>
            </div>

            <!-- Mod Info -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">الإصدار</div>
                    <div id="mod-version" class="info-value">جاري التحميل...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">تصنيف المود</div>
                    <div id="mod-category" class="info-value">جاري التحميل...</div>
                </div>
            </div>

            <!-- Description -->
            <div class="description-container">
                <div class="info-label text-center">الوصف</div>
                <div id="mod-description" class="info-value text-center" style="margin-top: 10px;">جاري تحميل الوصف...</div>
            </div>
        </div>

        <!-- Download Button -->
        <div class="download-section">
            <a id="download-button" class="download-button" href="#" onclick="handleDownload(event)">
                <span id="download-icon">📥</span>
                <span id="download-text">تحميل المود</span>
            </a>
        </div>
    </div>

    <script>
        // تم نقل JavaScript إلى script_fixed.js لتجنب التضارب
        // تم نقل جميع JavaScript إلى script_fixed.js
        // تم نقل جميع JavaScript إلى script_fixed.js لتجنب التضارب
        console.log('📄 index.html محمل - ينتظر script_fixed.js');
    </script>

    <!-- تحميل الملف المُصلح -->
    <script src="script_fixed.js"></script>
</body>
</html>
