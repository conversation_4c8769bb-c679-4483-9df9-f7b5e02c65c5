<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإصلاح المباشر</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #3d3d3d;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .warning { background: #ff9800; }
        .info { background: #2196F3; }
        button {
            background: #FFA500;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #FF8C00;
        }
        input {
            padding: 8px;
            border: 1px solid #555;
            background: #444;
            color: white;
            border-radius: 4px;
            margin: 5px;
        }
        .code {
            background: #000;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار الإصلاح المباشر لمشكلة modId</h1>
        
        <div class="test-section">
            <h2>🧪 اختبار دالة fixModId</h2>
            <p>اختبر دالة إصلاح معرف المود مع قيم مختلفة:</p>
            
            <input type="text" id="testInput" placeholder="أدخل قيمة للاختبار" value="1:1">
            <button onclick="testFixModId()">اختبار</button>
            <button onclick="runAllFixTests()">اختبار جميع الحالات</button>
            
            <div id="fixResults"></div>
        </div>
        
        <div class="test-section">
            <h2>🌐 اختبار اتصال Supabase</h2>
            <button onclick="testSupabaseConnection()">اختبار الاتصال</button>
            <button onclick="testModQuery()">اختبار استعلام المود</button>
            
            <div id="supabaseResults"></div>
        </div>
        
        <div class="test-section">
            <h2>📋 روابط اختبار مباشرة</h2>
            <p>اختبر الصفحة الأساسية مع الملف المُصلح:</p>
            <ul>
                <li><a href="index_fixed.html?id=1" style="color: #FFA500;">اختبار مع ID = 1</a></li>
                <li><a href="index_fixed.html?id=1:1" style="color: #FFA500;">اختبار مع ID = 1:1 (المشكلة الأصلية)</a></li>
                <li><a href="index_fixed.html?id=test-mod" style="color: #FFA500;">اختبار مع ID نصي</a></li>
                <li><a href="index_fixed.html?preview=1" style="color: #FFA500;">اختبار وضع المعاينة</a></li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>📝 تعليمات الإصلاح</h2>
            <div class="code">
                <strong>خطوات تطبيق الإصلاح:</strong><br>
                1. انسخ ملف script_fixed.js<br>
                2. استبدل به ملف script.js الأصلي<br>
                3. ارفع الملف على الاستضافة<br>
                4. اختبر الروابط أعلاه
            </div>
            
            <div class="code">
                <strong>أو أنشئ ملف index_fixed.html:</strong><br>
                - انسخ index.html<br>
                - غير script.js إلى script_fixed.js<br>
                - ارفع الملفين معاً
            </div>
        </div>
    </div>

    <script>
        // نسخة من دالة fixModId للاختبار
        function fixModId(rawId) {
            if (!rawId) return '1';
            
            let cleanId = String(rawId).trim();
            
            if (cleanId.includes(':')) {
                cleanId = cleanId.split(':')[0];
                console.log('Fixed modId from', rawId, 'to', cleanId);
            }
            
            cleanId = cleanId.replace(/[^a-zA-Z0-9\-_]/g, '');
            
            if (!cleanId) {
                cleanId = '1';
            }
            
            return cleanId;
        }

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
        }

        function testFixModId() {
            const input = document.getElementById('testInput').value;
            const result = fixModId(input);
            
            const isFixed = input !== result;
            const type = isFixed ? 'success' : 'info';
            const message = `"${input}" → "${result}" ${isFixed ? '(تم الإصلاح)' : '(لا يحتاج إصلاح)'}`;
            
            addResult('fixResults', message, type);
        }

        function runAllFixTests() {
            document.getElementById('fixResults').innerHTML = '';
            
            const testCases = [
                { input: '1:1', expected: '1' },
                { input: '1', expected: '1' },
                { input: 'test-mod', expected: 'test-mod' },
                { input: 'invalid@#$', expected: 'invalid' },
                { input: '', expected: '1' },
                { input: null, expected: '1' },
                { input: 'mod:with:colons', expected: 'mod' },
                { input: '  spaced  ', expected: 'spaced' }
            ];
            
            testCases.forEach(testCase => {
                const result = fixModId(testCase.input);
                const isCorrect = result === testCase.expected;
                const type = isCorrect ? 'success' : 'error';
                const message = `"${testCase.input}" → "${result}" ${isCorrect ? '✅' : '❌ (متوقع: ' + testCase.expected + ')'}`;
                
                addResult('fixResults', message, type);
            });
        }

        async function testSupabaseConnection() {
            const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
            const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";
            
            try {
                addResult('supabaseResults', '🔄 اختبار اتصال Supabase...', 'info');
                
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`
                    }
                });
                
                if (response.ok) {
                    addResult('supabaseResults', '✅ اتصال Supabase ناجح!', 'success');
                } else {
                    addResult('supabaseResults', `❌ فشل اتصال Supabase: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult('supabaseResults', `❌ خطأ في اتصال Supabase: ${error.message}`, 'error');
            }
        }

        async function testModQuery() {
            const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
            const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";
            
            try {
                addResult('supabaseResults', '🔄 اختبار استعلام المود...', 'info');
                
                // اختبار الاستعلام المُصلح
                const fixedId = fixModId('1:1'); // يجب أن يعطي '1'
                const url = `${SUPABASE_URL}/rest/v1/mods?id=eq.${fixedId}&limit=1`;
                
                addResult('supabaseResults', `🌐 رابط الاستعلام: ${url}`, 'info');
                
                const response = await fetch(url, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('supabaseResults', `✅ استعلام ناجح! عدد النتائج: ${data.length}`, 'success');
                    
                    if (data.length > 0) {
                        addResult('supabaseResults', `📦 اسم المود: ${data[0].name || 'غير محدد'}`, 'info');
                    }
                } else {
                    const errorText = await response.text();
                    addResult('supabaseResults', `❌ فشل الاستعلام: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                addResult('supabaseResults', `❌ خطأ في الاستعلام: ${error.message}`, 'error');
            }
        }

        // تشغيل اختبار أساسي عند التحميل
        window.addEventListener('load', () => {
            addResult('fixResults', '📄 تم تحميل صفحة الاختبار', 'success');
            addResult('supabaseResults', '🚀 جاهز لاختبار Supabase', 'info');
        });
    </script>
</body>
</html>
