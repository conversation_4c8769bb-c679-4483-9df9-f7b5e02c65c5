<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - Cloudflare Ready</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .warning { background: #ff9800; }
        .info { background: #2196F3; }
        .pending { background: #9E9E9E; }
        
        button {
            background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,165,0,0.4);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: #3d3d3d;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #FFA500;
        }
        .code {
            background: #000;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            border: 1px solid #444;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .highlight {
            background: #FFA500;
            color: #000;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .link-test {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            font-family: monospace;
            word-break: break-all;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .link-test:hover {
            background: #444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 اختبار سريع - Cloudflare Ready</h1>
        
        <div class="status info">
            <strong>🎯 الهدف:</strong> التأكد من أن جميع الملفات جاهزة للنشر على Cloudflare Pages
        </div>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>📄 اختبار الملفات</h3>
                <div id="fileTest" class="status pending">في الانتظار...</div>
                <button onclick="testFiles()">اختبار الملفات</button>
            </div>
            
            <div class="test-card">
                <h3>🔧 اختبار JavaScript</h3>
                <div id="jsTest" class="status pending">في الانتظار...</div>
                <button onclick="testJavaScript()">اختبار JS</button>
            </div>
            
            <div class="test-card">
                <h3>🎨 اختبار CSS</h3>
                <div id="cssTest" class="status pending">في الانتظار...</div>
                <button onclick="testCSS()">اختبار CSS</button>
            </div>
            
            <div class="test-card">
                <h3>🌐 اختبار الروابط</h3>
                <div id="linkTest" class="status pending">في الانتظار...</div>
                <button onclick="testLinks()">اختبار الروابط</button>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="runAllTests()" style="font-size: 18px; padding: 15px 30px;">
                🧪 تشغيل جميع الاختبارات
            </button>
        </div>
        
        <div class="test-card">
            <h3>📊 نتائج الاختبار</h3>
            <div id="testResults" class="code">
انتظار بدء الاختبارات...
            </div>
        </div>
        
        <div class="test-card">
            <h3>🔗 اختبار الروابط المباشر</h3>
            <p>اضغط على الروابط للاختبار:</p>
            <div class="link-test" onclick="testUrl('index.html')">
                <strong>🏠 الصفحة الرئيسية:</strong> index.html
            </div>
            <div class="link-test" onclick="testUrl('index.html?id=1')">
                <strong>📦 مود عادي:</strong> index.html?id=1
            </div>
            <div class="link-test" onclick="testUrl('index.html?id=1:1')">
                <strong>🔧 المشكلة الأصلية:</strong> index.html?id=1:1
            </div>
            <div class="link-test" onclick="testUrl('index.html?preview=1')">
                <strong>👁️ وضع المعاينة:</strong> index.html?preview=1
            </div>
        </div>
        
        <div class="test-card">
            <h3>✅ قائمة التحقق النهائية</h3>
            <div class="status warning">
                <strong>قبل النشر على Cloudflare:</strong>
                <ul style="text-align: right; margin: 10px 0;">
                    <li>✅ جميع الملفات موجودة</li>
                    <li>✅ JavaScript يعمل بدون أخطاء</li>
                    <li>✅ CSS يتم تحميله بنجاح</li>
                    <li>✅ الروابط تعمل بشكل صحيح</li>
                    <li>✅ لا توجد أخطاء في console</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('testResults');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }
        
        async function testFiles() {
            log('📄 اختبار وجود الملفات الأساسية...');
            
            const requiredFiles = [
                'index.html',
                'script.js',
                'style.css',
                'style-templates.css',
                '_headers',
                '_redirects',
                'robots.txt'
            ];
            
            let existingFiles = 0;
            
            for (const file of requiredFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        existingFiles++;
                        log(`✅ ${file} موجود`, 'success');
                    } else {
                        log(`❌ ${file} غير موجود (${response.status})`, 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في فحص ${file}: ${error.message}`, 'error');
                }
            }
            
            if (existingFiles === requiredFiles.length) {
                setStatus('fileTest', 'success', `✅ جميع الملفات موجودة (${existingFiles}/${requiredFiles.length})`);
            } else {
                setStatus('fileTest', 'warning', `⚠️ ${existingFiles}/${requiredFiles.length} ملفات موجودة`);
            }
        }
        
        function testJavaScript() {
            log('🔧 اختبار JavaScript...');
            
            try {
                // اختبار تحميل script.js
                const scripts = document.querySelectorAll('script[src="script.js"]');
                if (scripts.length > 0) {
                    log('✅ script.js محمل في الصفحة', 'success');
                } else {
                    log('⚠️ script.js غير محمل في الصفحة', 'warning');
                }
                
                // اختبار الدوال الأساسية
                if (typeof window.fixModId === 'function') {
                    log('✅ دالة fixModId متوفرة', 'success');
                    
                    // اختبار الدالة
                    const testResult = window.fixModId('1:1');
                    if (testResult === '1') {
                        log('✅ دالة fixModId تعمل بشكل صحيح', 'success');
                    } else {
                        log(`❌ دالة fixModId لا تعمل بشكل صحيح: ${testResult}`, 'error');
                    }
                } else {
                    log('⚠️ دالة fixModId غير متوفرة', 'warning');
                }
                
                // اختبار عدم وجود أخطاء
                const errors = [];
                window.addEventListener('error', (e) => {
                    errors.push(e.message);
                });
                
                if (errors.length === 0) {
                    setStatus('jsTest', 'success', '✅ JavaScript يعمل بدون أخطاء');
                } else {
                    setStatus('jsTest', 'error', `❌ ${errors.length} أخطاء JavaScript`);
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار JavaScript: ${error.message}`, 'error');
                setStatus('jsTest', 'error', '❌ خطأ في JavaScript');
            }
        }
        
        function testCSS() {
            log('🎨 اختبار CSS...');
            
            try {
                // اختبار تحميل CSS
                const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
                let loadedCSS = 0;
                
                stylesheets.forEach((link, index) => {
                    if (link.sheet) {
                        loadedCSS++;
                        log(`✅ CSS ${index + 1} محمل: ${link.href}`, 'success');
                    } else {
                        log(`❌ CSS ${index + 1} غير محمل: ${link.href}`, 'error');
                    }
                });
                
                // اختبار CSS variables
                const rootStyles = getComputedStyle(document.documentElement);
                const bgColor = rootStyles.getPropertyValue('--bg-color');
                
                if (bgColor) {
                    log('✅ CSS variables تعمل بشكل صحيح', 'success');
                } else {
                    log('⚠️ CSS variables غير متوفرة', 'warning');
                }
                
                if (loadedCSS === stylesheets.length && loadedCSS > 0) {
                    setStatus('cssTest', 'success', `✅ جميع ملفات CSS محملة (${loadedCSS})`);
                } else {
                    setStatus('cssTest', 'warning', `⚠️ ${loadedCSS}/${stylesheets.length} ملفات CSS محملة`);
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار CSS: ${error.message}`, 'error');
                setStatus('cssTest', 'error', '❌ خطأ في CSS');
            }
        }
        
        function testLinks() {
            log('🔗 اختبار الروابط...');
            
            const testUrls = [
                'index.html',
                'index.html?id=1',
                'index.html?id=1:1',
                'index.html?preview=1'
            ];
            
            let validLinks = 0;
            
            testUrls.forEach(url => {
                try {
                    const link = new URL(url, window.location.origin);
                    log(`✅ رابط صالح: ${url}`, 'success');
                    validLinks++;
                } catch (error) {
                    log(`❌ رابط غير صالح: ${url}`, 'error');
                }
            });
            
            if (validLinks === testUrls.length) {
                setStatus('linkTest', 'success', '✅ جميع الروابط صالحة');
            } else {
                setStatus('linkTest', 'warning', `⚠️ ${validLinks}/${testUrls.length} روابط صالحة`);
            }
        }
        
        function testUrl(url) {
            log(`🔗 فتح رابط: ${url}`);
            window.open(url, '_blank');
        }
        
        async function runAllTests() {
            log('🚀 بدء تشغيل جميع الاختبارات...');
            testLog = []; // مسح السجل
            
            await testFiles();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testJavaScript();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testCSS();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testLinks();
            
            log('🎉 انتهت جميع الاختبارات!');
            log('📋 راجع النتائج أعلاه للتأكد من الجاهزية');
        }
        
        // تشغيل اختبار أساسي عند التحميل
        window.addEventListener('load', () => {
            log('📄 تم تحميل صفحة الاختبار');
            log('💡 اضغط على "تشغيل جميع الاختبارات" للبدء');
        });
    </script>
</body>
</html>
