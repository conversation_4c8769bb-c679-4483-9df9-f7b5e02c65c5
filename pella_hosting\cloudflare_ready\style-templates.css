/*
 * قوالب التصميم المختلفة لصفحة المودات
 * Style Templates for Mod Page
 * Cloudflare Pages Version - READY FOR DEPLOYMENT
 */

/* ========== Default Template ========== */
.style-template-default {
    /* استخدام الستايل الافتراضي من style.css */
}

/* ========== Telegram Style Template ========== */
.style-template-telegram {
    font-family: 'Roboto', sans-serif;
}

.style-template-telegram body {
    background: linear-gradient(135deg, #0088cc 0%, #40a7e3 100%);
}

.style-template-telegram .mod-info-card,
.style-template-telegram .mod-description-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 136, 204, 0.3);
    color: #333;
}

.style-template-telegram .download-button {
    background: linear-gradient(45deg, #40a7e3, #64b5f6);
    border: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    color: white;
}

.style-template-telegram .download-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(64, 167, 227, 0.4);
}

.style-template-telegram .nav-button {
    background: #40a7e3;
    border-radius: 50%;
}

.style-template-telegram .thumbnail.active {
    border-color: #40a7e3;
    box-shadow: 0 0 8px rgba(64, 167, 227, 0.6);
}

.style-template-telegram .detail-label {
    color: #0088cc;
}

.style-template-telegram header {
    background: linear-gradient(135deg, #0088cc 0%, #40a7e3 100%);
}

/* ========== TikTok Style Template ========== */
.style-template-tiktok {
    font-family: 'Poppins', sans-serif;
}

.style-template-tiktok body {
    background: linear-gradient(45deg, #000000 0%, #1a1a1a 50%, #000000 100%);
    color: #ffffff;
}

.style-template-tiktok .mod-info-card,
.style-template-tiktok .mod-description-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid #ff0050;
    border-radius: 20px;
    box-shadow: 0 0 20px rgba(255, 0, 80, 0.3);
}

.style-template-tiktok .download-button {
    background: linear-gradient(45deg, #ff0050, #25f4ee);
    border: none;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: tiktok-pulse 2s infinite;
}

@keyframes tiktok-pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 0, 80, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 0, 80, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 0, 80, 0); }
}

.style-template-tiktok .nav-button {
    background: #ff0050;
    border-radius: 50%;
}

.style-template-tiktok .thumbnail.active {
    border-color: #ff0050;
    box-shadow: 0 0 8px rgba(255, 0, 80, 0.6);
}

.style-template-tiktok .detail-label {
    color: #25f4ee;
}

.style-template-tiktok header {
    background: linear-gradient(135deg, #ff0050 0%, #25f4ee 100%);
}

/* ========== YouTube Style Template ========== */
.style-template-youtube {
    font-family: 'Roboto', sans-serif;
}

.style-template-youtube body {
    background: #0f0f0f;
    color: #ffffff;
}

.style-template-youtube .mod-info-card,
.style-template-youtube .mod-description-card {
    background: #1f1f1f;
    border-radius: 12px;
    border: 1px solid #303030;
}

.style-template-youtube .download-button {
    background: #ff0000;
    border-radius: 2px;
    font-weight: 500;
    text-transform: uppercase;
}

.style-template-youtube .download-button:hover {
    background: #cc0000;
}

.style-template-youtube .nav-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.style-template-youtube .thumbnail.active {
    border-color: #ff0000;
}

.style-template-youtube header {
    background: #212121;
}

/* ========== Instagram Style Template ========== */
.style-template-instagram {
    font-family: 'Segoe UI', sans-serif;
}

.style-template-instagram body {
    background: linear-gradient(45deg, #405de6, #5851db, #833ab4, #c13584, #e1306c, #fd1d1d);
}

.style-template-instagram .mod-info-card,
.style-template-instagram .mod-description-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    color: #262626;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.style-template-instagram .download-button {
    background: linear-gradient(45deg, #405de6, #833ab4, #c13584, #e1306c);
    border-radius: 25px;
    font-weight: 600;
}

.style-template-instagram .nav-button {
    background: rgba(255, 255, 255, 0.9);
    color: #262626;
}

.style-template-instagram .thumbnail.active {
    border-color: #e1306c;
    box-shadow: 0 0 10px rgba(225, 48, 108, 0.5);
}

.style-template-instagram header {
    background: linear-gradient(45deg, #405de6, #833ab4, #c13584);
}

/* ========== Discord Style Template ========== */
.style-template-discord {
    font-family: 'Whitney', 'Helvetica Neue', sans-serif;
}

.style-template-discord body {
    background: #36393f;
    color: #dcddde;
}

.style-template-discord .mod-info-card,
.style-template-discord .mod-description-card {
    background: #2f3136;
    border-radius: 8px;
    border: 1px solid #202225;
}

.style-template-discord .download-button {
    background: #5865f2;
    border-radius: 3px;
    font-weight: 500;
}

.style-template-discord .download-button:hover {
    background: #4752c4;
}

.style-template-discord .nav-button {
    background: #5865f2;
}

.style-template-discord .thumbnail.active {
    border-color: #5865f2;
}

.style-template-discord header {
    background: #5865f2;
}

/* ========== Gaming Style Template ========== */
.style-template-gaming {
    font-family: 'Orbitron', monospace;
}

.style-template-gaming body {
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #00ff41;
}

.style-template-gaming .mod-info-card,
.style-template-gaming .mod-description-card {
    background: rgba(0, 255, 65, 0.1);
    border: 2px solid #00ff41;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
}

.style-template-gaming .download-button {
    background: linear-gradient(45deg, #00ff41, #00cc33);
    border: 2px solid #00ff41;
    border-radius: 0;
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 2px;
    animation: gaming-glow 2s ease-in-out infinite alternate;
}

@keyframes gaming-glow {
    from { box-shadow: 0 0 5px #00ff41, 0 0 10px #00ff41, 0 0 15px #00ff41; }
    to { box-shadow: 0 0 10px #00ff41, 0 0 20px #00ff41, 0 0 30px #00ff41; }
}

.style-template-gaming .nav-button {
    background: #00ff41;
    color: #000;
    border: 2px solid #00ff41;
    border-radius: 0;
}

.style-template-gaming .thumbnail.active {
    border-color: #00ff41;
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.8);
}

.style-template-gaming header {
    background: linear-gradient(135deg, #0f3460 0%, #00ff41 100%);
}

/* ========== Responsive Adjustments ========== */
@media (max-width: 768px) {
    .style-template-gaming .download-button {
        letter-spacing: 1px;
        font-size: 0.9rem;
    }
    
    .style-template-tiktok .download-button {
        font-size: 0.9rem;
    }
    
    .style-template-instagram .download-button {
        font-size: 1rem;
    }
}

/* ========== Animation Overrides for Reduced Motion ========== */
@media (prefers-reduced-motion: reduce) {
    .style-template-tiktok .download-button,
    .style-template-gaming .download-button {
        animation: none;
    }
    
    @keyframes tiktok-pulse,
    @keyframes gaming-glow {
        from, to { 
            box-shadow: none; 
        }
    }
}
