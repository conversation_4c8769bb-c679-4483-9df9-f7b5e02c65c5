<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تشخيص المشاكل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .warning { background: #ff9800; }
        .info { background: #2196F3; }
        .pending { background: #9E9E9E; }
        
        button {
            background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,165,0,0.4);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: #3d3d3d;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #FFA500;
        }
        .code {
            background: #000;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            border: 1px solid #444;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .highlight {
            background: #FFA500;
            color: #000;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .url-test {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            font-family: monospace;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص شامل لمشاكل البوت</h1>
        
        <div class="status info">
            <strong>🎯 الهدف:</strong> تشخيص وحل جميع المشاكل المكتشفة:
            <ul>
                <li>❌ <span class="highlight">Identifier 'modData' has already been declared</span></li>
                <li>❌ <span class="highlight">ytqxxodyecdeosnqoure.supabase.co/rest/v1/mods?id=eq.1&select=*:1 Failed to load resource: 400</span></li>
                <li>❌ <span class="highlight">net::ERR_NAME_NOT_RESOLVED</span></li>
            </ul>
        </div>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🔧 اختبار تضارب المتغيرات</h3>
                <div id="variableTest" class="status pending">في الانتظار...</div>
                <button onclick="testVariableConflict()">اختبار التضارب</button>
            </div>
            
            <div class="test-card">
                <h3>🌐 اختبار استعلامات Supabase</h3>
                <div id="supabaseTest" class="status pending">في الانتظار...</div>
                <button onclick="testSupabaseQueries()">اختبار الاستعلامات</button>
            </div>
            
            <div class="test-card">
                <h3>🖼️ اختبار تحميل الصور</h3>
                <div id="imageTest" class="status pending">في الانتظار...</div>
                <button onclick="testImageLoading()">اختبار الصور</button>
            </div>
            
            <div class="test-card">
                <h3>📄 اختبار تحميل الملفات</h3>
                <div id="fileTest" class="status pending">في الانتظار...</div>
                <button onclick="testFileLoading()">اختبار الملفات</button>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="runFullDiagnosis()" style="font-size: 18px; padding: 15px 30px;">
                🔍 تشخيص شامل
            </button>
            <button onclick="clearResults()" style="background: #666;">
                🗑️ مسح النتائج
            </button>
        </div>
        
        <div class="test-card">
            <h3>📊 سجل التشخيص المفصل</h3>
            <div id="diagnosticLog" class="code">
انتظار بدء التشخيص...
            </div>
        </div>
        
        <div class="test-card">
            <h3>🔗 اختبار الروابط المختلفة</h3>
            <div id="urlTests">
                <div class="url-test" onclick="testUrl('index_clean.html?id=1')">
                    <strong>✅ الرابط النظيف:</strong> index_clean.html?id=1
                </div>
                <div class="url-test" onclick="testUrl('index_clean.html?id=1:1')">
                    <strong>🔧 المشكلة الأصلية:</strong> index_clean.html?id=1:1
                </div>
                <div class="url-test" onclick="testUrl('index.html?id=1')">
                    <strong>⚠️ الملف القديم:</strong> index.html?id=1
                </div>
                <div class="url-test" onclick="testUrl('index_clean.html?preview=1')">
                    <strong>👁️ وضع المعاينة:</strong> index_clean.html?preview=1
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h3>💡 الحلول المقترحة</h3>
            <div class="status warning">
                <strong>للحل النهائي:</strong>
                <ol>
                    <li>استخدم <code>index_clean.html</code> بدلاً من <code>index.html</code></li>
                    <li>تأكد من تحميل <code>script_fixed.js</code> فقط</li>
                    <li>تحقق من إعدادات Supabase RLS</li>
                    <li>استخدم صور من مصادر موثوقة</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let diagnosticResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            diagnosticResults.push(logEntry);
            
            const logElement = document.getElementById('diagnosticLog');
            logElement.textContent = diagnosticResults.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }
        
        function clearResults() {
            diagnosticResults = [];
            document.getElementById('diagnosticLog').textContent = 'تم مسح النتائج...';
            
            ['variableTest', 'supabaseTest', 'imageTest', 'fileTest'].forEach(id => {
                setStatus(id, 'pending', 'في الانتظار...');
            });
        }
        
        function testVariableConflict() {
            log('🔧 اختبار تضارب المتغيرات...');
            
            try {
                // محاولة إنشاء متغير modData
                if (typeof window.modData !== 'undefined') {
                    log('⚠️ متغير modData موجود مسبقاً', 'warning');
                    setStatus('variableTest', 'warning', '⚠️ متغير موجود مسبقاً');
                } else {
                    log('✅ لا يوجد تضارب في المتغيرات', 'success');
                    setStatus('variableTest', 'success', '✅ لا يوجد تضارب');
                }
                
                // اختبار تحميل script_fixed.js
                if (typeof window.fixModIdUrgent === 'function') {
                    log('✅ script_fixed.js محمل بنجاح', 'success');
                } else {
                    log('❌ script_fixed.js غير محمل', 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار التضارب: ${error.message}`, 'error');
                setStatus('variableTest', 'error', '❌ خطأ في الاختبار');
            }
        }
        
        async function testSupabaseQueries() {
            log('🌐 اختبار استعلامات Supabase...');
            
            const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
            const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";
            
            const testQueries = [
                // الاستعلام المشكل
                `${SUPABASE_URL}/rest/v1/mods?id=eq.1&select=*`,
                
                // الاستعلام المُصلح
                `${SUPABASE_URL}/rest/v1/mods?id=eq.1`,
                
                // استعلام بسيط
                `${SUPABASE_URL}/rest/v1/mods?limit=1`,
                
                // اختبار الاتصال
                `${SUPABASE_URL}/rest/v1/`
            ];
            
            let successCount = 0;
            
            for (let i = 0; i < testQueries.length; i++) {
                try {
                    log(`🔍 اختبار الاستعلام ${i + 1}: ${testQueries[i]}`);
                    
                    const response = await fetch(testQueries[i], {
                        headers: {
                            'apikey': SUPABASE_KEY,
                            'Authorization': `Bearer ${SUPABASE_KEY}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (response.ok) {
                        log(`✅ الاستعلام ${i + 1} نجح: ${response.status}`, 'success');
                        successCount++;
                    } else {
                        const errorText = await response.text();
                        log(`❌ الاستعلام ${i + 1} فشل: ${response.status} - ${errorText}`, 'error');
                    }
                    
                } catch (error) {
                    log(`❌ خطأ في الاستعلام ${i + 1}: ${error.message}`, 'error');
                }
            }
            
            if (successCount > 0) {
                setStatus('supabaseTest', 'success', `✅ نجح ${successCount}/${testQueries.length} استعلام`);
            } else {
                setStatus('supabaseTest', 'error', '❌ فشلت جميع الاستعلامات');
            }
        }
        
        function testImageLoading() {
            log('🖼️ اختبار تحميل الصور...');
            
            const testImages = [
                'https://via.placeholder.com/800x450/4F46E5/FFFFFF?text=Test+Image',
                'https://picsum.photos/800/450',
                'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100"%3E%3Crect width="100" height="100" fill="%234F46E5"/%3E%3C/svg%3E'
            ];
            
            let loadedCount = 0;
            let totalImages = testImages.length;
            
            testImages.forEach((src, index) => {
                const img = new Image();
                
                img.onload = () => {
                    loadedCount++;
                    log(`✅ تم تحميل الصورة ${index + 1}: ${src}`, 'success');
                    
                    if (loadedCount === totalImages) {
                        setStatus('imageTest', 'success', '✅ تم تحميل جميع الصور');
                    }
                };
                
                img.onerror = () => {
                    log(`❌ فشل تحميل الصورة ${index + 1}: ${src}`, 'error');
                    
                    if (loadedCount + (index + 1) === totalImages) {
                        setStatus('imageTest', 'warning', `⚠️ تم تحميل ${loadedCount}/${totalImages} صور`);
                    }
                };
                
                img.src = src;
            });
        }
        
        function testFileLoading() {
            log('📄 اختبار تحميل الملفات...');
            
            const testFiles = [
                'script_fixed.js',
                'style.css',
                'index_clean.html'
            ];
            
            let checkedCount = 0;
            let existingCount = 0;
            
            testFiles.forEach(async (file, index) => {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    checkedCount++;
                    
                    if (response.ok) {
                        existingCount++;
                        log(`✅ الملف موجود: ${file}`, 'success');
                    } else {
                        log(`❌ الملف غير موجود: ${file} (${response.status})`, 'error');
                    }
                    
                    if (checkedCount === testFiles.length) {
                        if (existingCount === testFiles.length) {
                            setStatus('fileTest', 'success', '✅ جميع الملفات موجودة');
                        } else {
                            setStatus('fileTest', 'warning', `⚠️ ${existingCount}/${testFiles.length} ملفات موجودة`);
                        }
                    }
                    
                } catch (error) {
                    checkedCount++;
                    log(`❌ خطأ في فحص الملف ${file}: ${error.message}`, 'error');
                    
                    if (checkedCount === testFiles.length) {
                        setStatus('fileTest', 'error', '❌ خطأ في فحص الملفات');
                    }
                }
            });
        }
        
        function testUrl(url) {
            log(`🔗 اختبار الرابط: ${url}`);
            window.open(url, '_blank');
        }
        
        async function runFullDiagnosis() {
            log('🚀 بدء التشخيص الشامل...');
            clearResults();
            
            await new Promise(resolve => setTimeout(resolve, 500));
            testVariableConflict();
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testSupabaseQueries();
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            testImageLoading();
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            testFileLoading();
            
            log('🎉 انتهى التشخيص الشامل!');
        }
        
        // تشغيل اختبار أساسي عند التحميل
        window.addEventListener('load', () => {
            log('📄 تم تحميل صفحة التشخيص');
            log('💡 اضغط على "تشخيص شامل" للبدء');
        });
    </script>
</body>
</html>
