<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح المشاكل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .warning { background: #ff9800; }
        .info { background: #2196F3; }
        button {
            background: #FFA500;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #FF8C00;
        }
        #loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #FFA500;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار إصلاح مشاكل بوت المودات</h1>
        
        <div id="loading">
            <div class="spinner"></div>
            <p>جاري الاختبار...</p>
        </div>
        
        <div id="results"></div>
        
        <div style="margin-top: 20px;">
            <button onclick="testSupabaseConnection()">اختبار اتصال Supabase</button>
            <button onclick="testModDataRetrieval()">اختبار جلب بيانات المود</button>
            <button onclick="testParameterExtraction()">اختبار استخراج المعاملات</button>
            <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <button onclick="clearResults()">مسح النتائج</button>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>روابط اختبار:</h3>
            <ul>
                <li><a href="index.html?id=1" style="color: #FFA500;">اختبار مع ID = 1</a></li>
                <li><a href="index.html?id=test-mod" style="color: #FFA500;">اختبار مع ID نصي</a></li>
                <li><a href="index.html?preview=1" style="color: #FFA500;">اختبار وضع المعاينة</a></li>
                <li><a href="index.html?id=1&lang=en" style="color: #FFA500;">اختبار باللغة الإنجليزية</a></li>
            </ul>
        </div>
    </div>

    <script>
        // إعدادات Supabase (نفس الإعدادات من الملف الأصلي)
        const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
        const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testSupabaseConnection() {
            showLoading(true);
            addResult('🔄 اختبار اتصال Supabase...', 'info');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`
                    }
                });
                
                if (response.ok) {
                    addResult('✅ اتصال Supabase ناجح!', 'success');
                } else {
                    addResult(`❌ فشل اتصال Supabase: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في اتصال Supabase: ${error.message}`, 'error');
            }
            
            showLoading(false);
        }

        async function testModDataRetrieval() {
            showLoading(true);
            addResult('🔄 اختبار جلب بيانات المود...', 'info');
            
            try {
                // اختبار جلب جميع المودات أولاً
                const allModsResponse = await fetch(`${SUPABASE_URL}/rest/v1/mods?limit=5`, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (allModsResponse.ok) {
                    const allMods = await allModsResponse.json();
                    addResult(`✅ تم جلب ${allMods.length} مود من قاعدة البيانات`, 'success');
                    
                    if (allMods.length > 0) {
                        const firstMod = allMods[0];
                        addResult(`📋 أول مود: ${firstMod.name || 'بدون اسم'} (ID: ${firstMod.id})`, 'info');
                        
                        // اختبار جلب مود محدد
                        const specificModResponse = await fetch(`${SUPABASE_URL}/rest/v1/mods?id=eq.${firstMod.id}`, {
                            headers: {
                                'apikey': SUPABASE_KEY,
                                'Authorization': `Bearer ${SUPABASE_KEY}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (specificModResponse.ok) {
                            const specificMod = await specificModResponse.json();
                            if (specificMod.length > 0) {
                                addResult('✅ تم جلب مود محدد بنجاح!', 'success');
                            } else {
                                addResult('⚠️ لم يتم العثور على المود المحدد', 'warning');
                            }
                        } else {
                            addResult(`❌ فشل جلب المود المحدد: ${specificModResponse.status}`, 'error');
                        }
                    } else {
                        addResult('⚠️ لا توجد مودات في قاعدة البيانات', 'warning');
                    }
                } else {
                    addResult(`❌ فشل جلب المودات: ${allModsResponse.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ خطأ في جلب البيانات: ${error.message}`, 'error');
            }
            
            showLoading(false);
        }

        function testParameterExtraction() {
            addResult('🔄 اختبار استخراج المعاملات...', 'info');
            
            // اختبار قيم مختلفة
            const testCases = [
                { input: '1', expected: '1' },
                { input: '1:1', expected: '1' },
                { input: 'test-mod', expected: 'test-mod' },
                { input: 'invalid@#$', expected: '1' },
                { input: '', expected: '1' }
            ];
            
            testCases.forEach(testCase => {
                const result = cleanModId(testCase.input);
                if (result === testCase.expected) {
                    addResult(`✅ "${testCase.input}" → "${result}"`, 'success');
                } else {
                    addResult(`❌ "${testCase.input}" → "${result}" (متوقع: "${testCase.expected}")`, 'error');
                }
            });
        }

        function cleanModId(rawModId) {
            if (!rawModId) return '1';
            
            rawModId = rawModId.toString().trim();
            
            if (rawModId.includes(':')) {
                rawModId = rawModId.split(':')[0];
            }
            
            if (!/^[a-zA-Z0-9-_]+$/.test(rawModId)) {
                return '1';
            }
            
            return rawModId;
        }

        async function runAllTests() {
            clearResults();
            addResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            await testSupabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testModDataRetrieval();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testParameterExtraction();
            
            addResult('🎉 انتهت جميع الاختبارات!', 'success');
        }

        // تشغيل اختبار أساسي عند تحميل الصفحة
        window.addEventListener('load', () => {
            addResult('📄 تم تحميل صفحة الاختبار بنجاح', 'success');
            addResult('💡 اضغط على الأزرار أعلاه لتشغيل الاختبارات', 'info');
        });
    </script>
</body>
</html>
