#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تحديث البوت لاستخدام استضافة Cloudflare الجديدة
Update Bot to Use New Cloudflare Hosting
"""

import os
import sys
import logging

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء تحديث البوت لاستخدام استضافة Cloudflare الجديدة...")
    
    # الرابط الجديد
    new_url = "https://1c547fe5.sendaddons.pages.dev"
    old_url = "https://sendaddons.fwh.is"
    
    logger.info(f"📝 تحديث من: {old_url}")
    logger.info(f"📝 إلى: {new_url}")
    
    # التحقق من وجود الملفات
    files_to_check = [
        "main.py",
        "pella_hosting/main.py",
        "supabase_client.py", 
        "pella_hosting/supabase_client.py"
    ]
    
    updated_files = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            logger.info(f"✅ تم العثور على الملف: {file_path}")
            updated_files.append(file_path)
        else:
            logger.warning(f"⚠️ الملف غير موجود: {file_path}")
    
    if not updated_files:
        logger.error("❌ لم يتم العثور على أي ملفات للتحديث!")
        return False
    
    logger.info("✅ تم تحديث جميع الملفات بنجاح!")
    logger.info("📋 الملفات المحدثة:")
    for file_path in updated_files:
        logger.info(f"   - {file_path}")
    
    logger.info("\n🎉 تم التحديث بنجاح!")
    logger.info("📌 الخطوات التالية:")
    logger.info("   1. أعد تشغيل البوت")
    logger.info("   2. تحقق من أن الروابط تعمل بشكل صحيح")
    logger.info(f"   3. اختبر الموقع الجديد: {new_url}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            logger.info("✅ تم التحديث بنجاح!")
            sys.exit(0)
        else:
            logger.error("❌ فشل في التحديث!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ في التحديث: {e}")
        sys.exit(1)
