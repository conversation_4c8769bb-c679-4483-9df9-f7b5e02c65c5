#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت التحقق من تحديث استضافة Cloudflare
Verify Cloudflare Hosting Update
"""

import os
import re
import sys
import logging

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_file_for_old_urls(file_path):
    """فحص ملف للبحث عن الروابط القديمة"""
    old_patterns = [
        r'sendaddons\.fwh\.is',
        r'https://sendaddons\.fwh\.is'
    ]

    new_pattern = r'1c547fe5\.sendaddons\.pages\.dev'

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        old_matches = []
        for pattern in old_patterns:
            matches = re.findall(pattern, content)
            old_matches.extend(matches)

        # تجاهل الروابط القديمة في شروط التحقق من الصور المرفوعة
        if 'supabase_client.py' in file_path:
            # فلترة الروابط القديمة التي تظهر في شروط التحقق من الصور
            filtered_matches = []
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'sendaddons.fwh.is' in line and 'uploaded_images' in line and ('if ' in line or 'or ' in line):
                    # هذا شرط للتحقق من الصور القديمة - مقبول
                    continue
                elif 'sendaddons.fwh.is' in line:
                    filtered_matches.extend(re.findall(r'sendaddons\.fwh\.is', line))
            old_matches = filtered_matches

        new_matches = re.findall(new_pattern, content)

        return {
            'old_urls': len(old_matches),
            'new_urls': len(new_matches),
            'old_matches': old_matches,
            'updated': len(old_matches) == 0 and len(new_matches) > 0
        }

    except Exception as e:
        logger.error(f"خطأ في قراءة الملف {file_path}: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    logger.info("🔍 فحص تحديث استضافة Cloudflare...")
    
    # الملفات المطلوب فحصها
    files_to_check = [
        "main.py",
        "pella_hosting/main.py", 
        "supabase_client.py",
        "pella_hosting/supabase_client.py"
    ]
    
    all_updated = True
    total_old = 0
    total_new = 0
    
    logger.info("📋 نتائج الفحص:")
    logger.info("=" * 50)
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            logger.warning(f"⚠️ الملف غير موجود: {file_path}")
            continue
            
        result = check_file_for_old_urls(file_path)
        if result is None:
            continue
            
        status = "✅ محدث" if result['updated'] else "❌ يحتاج تحديث"
        logger.info(f"{status} | {file_path}")
        logger.info(f"   الروابط القديمة: {result['old_urls']}")
        logger.info(f"   الروابط الجديدة: {result['new_urls']}")
        
        if result['old_urls'] > 0:
            logger.warning(f"   ⚠️ روابط قديمة موجودة: {result['old_matches']}")
            all_updated = False
            
        total_old += result['old_urls']
        total_new += result['new_urls']
        logger.info("-" * 30)
    
    logger.info("=" * 50)
    logger.info("📊 الملخص النهائي:")
    logger.info(f"   إجمالي الروابط القديمة: {total_old}")
    logger.info(f"   إجمالي الروابط الجديدة: {total_new}")
    
    if all_updated and total_new > 0:
        logger.info("🎉 تم التحديث بنجاح! جميع الروابط محدثة.")
        logger.info("✅ البوت جاهز لاستخدام استضافة Cloudflare الجديدة")
        logger.info(f"🌐 الرابط الجديد: https://1c547fe5.sendaddons.pages.dev")
        return True
    elif total_old > 0:
        logger.error("❌ لا يزال هناك روابط قديمة تحتاج تحديث!")
        return False
    else:
        logger.warning("⚠️ لم يتم العثور على أي روابط جديدة!")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            logger.info("\n🚀 يمكنك الآن إعادة تشغيل البوت!")
            sys.exit(0)
        else:
            logger.error("\n❌ يرجى مراجعة الأخطاء أعلاه!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ في الفحص: {e}")
        sys.exit(1)
