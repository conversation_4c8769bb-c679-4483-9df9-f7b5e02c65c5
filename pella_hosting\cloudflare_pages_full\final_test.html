<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - إصلاح مشكلة HTTP 400</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .warning { background: #ff9800; }
        .info { background: #2196F3; }
        .pending { background: #9E9E9E; }
        
        button {
            background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255,165,0,0.4);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: #3d3d3d;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #FFA500;
        }
        .code {
            background: #000;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            border: 1px solid #444;
        }
        .highlight {
            background: #FFA500;
            color: #000;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #444;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار نهائي - إصلاح مشكلة HTTP 400</h1>
        
        <div class="status info">
            <strong>📋 الهدف:</strong> التأكد من حل مشكلة <span class="highlight">ytqxxodyecdeosnqoure.supabase.co/rest/v1/mods?id=eq.1:1</span>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="overallProgress"></div>
        </div>
        <p id="progressText">جاهز للبدء...</p>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🧪 اختبار دالة الإصلاح</h3>
                <div id="fixTest" class="status pending">في الانتظار...</div>
                <button onclick="testFixFunction()">اختبار الإصلاح</button>
            </div>
            
            <div class="test-card">
                <h3>🌐 اختبار اتصال Supabase</h3>
                <div id="connectionTest" class="status pending">في الانتظار...</div>
                <button onclick="testConnection()">اختبار الاتصال</button>
            </div>
            
            <div class="test-card">
                <h3>📦 اختبار جلب البيانات</h3>
                <div id="dataTest" class="status pending">في الانتظار...</div>
                <button onclick="testDataFetch()">اختبار البيانات</button>
            </div>
            
            <div class="test-card">
                <h3>🔗 اختبار الروابط</h3>
                <div id="linkTest" class="status pending">في الانتظار...</div>
                <button onclick="testLinks()">اختبار الروابط</button>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="runAllTests()" style="font-size: 18px; padding: 15px 30px;">
                🚀 تشغيل جميع الاختبارات
            </button>
        </div>
        
        <div class="test-card">
            <h3>📊 نتائج مفصلة</h3>
            <div id="detailedResults" class="code">
                انتظار بدء الاختبارات...
            </div>
        </div>
        
        <div class="test-card">
            <h3>🔗 روابط الاختبار المباشر</h3>
            <p>اختبر هذه الروابط بعد تطبيق الإصلاح:</p>
            <ul>
                <li><a href="index.html?id=1:1" style="color: #FFA500;">المشكلة الأصلية: ?id=1:1</a></li>
                <li><a href="index.html?id=1" style="color: #FFA500;">الرابط الصحيح: ?id=1</a></li>
                <li><a href="index.html?id=test:mod" style="color: #FFA500;">اختبار آخر: ?id=test:mod</a></li>
                <li><a href="index.html?preview=1" style="color: #FFA500;">وضع المعاينة</a></li>
            </ul>
        </div>
        
        <div class="status warning">
            <strong>⚠️ تذكير:</strong> تأكد من رفع الملفات المحدثة على الاستضافة قبل الاختبار النهائي!
        </div>
    </div>

    <script>
        let testResults = {
            fix: false,
            connection: false,
            data: false,
            links: false
        };
        
        function updateProgress() {
            const completed = Object.values(testResults).filter(Boolean).length;
            const total = Object.keys(testResults).length;
            const percentage = (completed / total) * 100;
            
            document.getElementById('overallProgress').style.width = percentage + '%';
            document.getElementById('progressText').textContent = 
                `تم إكمال ${completed} من ${total} اختبارات (${Math.round(percentage)}%)`;
        }
        
        function logResult(message) {
            const results = document.getElementById('detailedResults');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `[${timestamp}] ${message}\n`;
            results.scrollTop = results.scrollHeight;
        }
        
        function setTestStatus(testId, status, message) {
            const element = document.getElementById(testId);
            element.className = `status ${status}`;
            element.textContent = message;
        }
        
        // دالة إصلاح modId للاختبار
        function fixModId(rawId) {
            if (!rawId) return '1';
            let cleanId = String(rawId).trim();
            if (cleanId.includes(':')) {
                cleanId = cleanId.split(':')[0];
                logResult(`🔧 تم إصلاح modId من "${rawId}" إلى "${cleanId}"`);
            }
            cleanId = cleanId.replace(/[^a-zA-Z0-9\-_]/g, '');
            return cleanId || '1';
        }
        
        async function testFixFunction() {
            logResult('🧪 بدء اختبار دالة الإصلاح...');
            
            const testCases = [
                { input: '1:1', expected: '1' },
                { input: '1', expected: '1' },
                { input: 'test:mod', expected: 'test' },
                { input: 'invalid@#$', expected: 'invalid' },
                { input: '', expected: '1' },
                { input: null, expected: '1' }
            ];
            
            let passed = 0;
            for (const testCase of testCases) {
                const result = fixModId(testCase.input);
                if (result === testCase.expected) {
                    passed++;
                    logResult(`✅ "${testCase.input}" → "${result}" (صحيح)`);
                } else {
                    logResult(`❌ "${testCase.input}" → "${result}" (متوقع: "${testCase.expected}")`);
                }
            }
            
            if (passed === testCases.length) {
                setTestStatus('fixTest', 'success', `✅ نجح ${passed}/${testCases.length} اختبار`);
                testResults.fix = true;
            } else {
                setTestStatus('fixTest', 'error', `❌ نجح ${passed}/${testCases.length} اختبار فقط`);
            }
            
            updateProgress();
        }
        
        async function testConnection() {
            logResult('🌐 اختبار اتصال Supabase...');
            
            const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
            const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`
                    }
                });
                
                if (response.ok) {
                    setTestStatus('connectionTest', 'success', '✅ الاتصال ناجح');
                    logResult('✅ اتصال Supabase ناجح');
                    testResults.connection = true;
                } else {
                    setTestStatus('connectionTest', 'error', `❌ فشل الاتصال: ${response.status}`);
                    logResult(`❌ فشل اتصال Supabase: ${response.status}`);
                }
            } catch (error) {
                setTestStatus('connectionTest', 'error', '❌ خطأ في الاتصال');
                logResult(`❌ خطأ في اتصال Supabase: ${error.message}`);
            }
            
            updateProgress();
        }
        
        async function testDataFetch() {
            logResult('📦 اختبار جلب البيانات...');
            
            const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
            const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4";
            
            try {
                // اختبار الاستعلام المُصلح
                const fixedId = fixModId('1:1');
                const url = `${SUPABASE_URL}/rest/v1/mods?id=eq.${fixedId}&limit=1`;
                
                logResult(`🔗 رابط الاستعلام: ${url}`);
                
                const response = await fetch(url, {
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    setTestStatus('dataTest', 'success', `✅ تم جلب ${data.length} نتيجة`);
                    logResult(`✅ نجح جلب البيانات: ${data.length} نتيجة`);
                    if (data.length > 0) {
                        logResult(`📋 اسم المود: ${data[0].name || 'غير محدد'}`);
                    }
                    testResults.data = true;
                } else {
                    const errorText = await response.text();
                    setTestStatus('dataTest', 'error', `❌ فشل: ${response.status}`);
                    logResult(`❌ فشل جلب البيانات: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                setTestStatus('dataTest', 'error', '❌ خطأ في الجلب');
                logResult(`❌ خطأ في جلب البيانات: ${error.message}`);
            }
            
            updateProgress();
        }
        
        function testLinks() {
            logResult('🔗 اختبار الروابط...');
            
            const testUrls = [
                'index.html?id=1',
                'index.html?id=1:1',
                'index.html?preview=1'
            ];
            
            let validLinks = 0;
            testUrls.forEach(url => {
                try {
                    const link = new URL(url, window.location.origin);
                    logResult(`✅ رابط صالح: ${url}`);
                    validLinks++;
                } catch (error) {
                    logResult(`❌ رابط غير صالح: ${url}`);
                }
            });
            
            if (validLinks === testUrls.length) {
                setTestStatus('linkTest', 'success', `✅ جميع الروابط صالحة`);
                testResults.links = true;
            } else {
                setTestStatus('linkTest', 'warning', `⚠️ ${validLinks}/${testUrls.length} روابط صالحة`);
            }
            
            updateProgress();
        }
        
        async function runAllTests() {
            logResult('🚀 بدء تشغيل جميع الاختبارات...');
            
            await testFixFunction();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testDataFetch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testLinks();
            
            const allPassed = Object.values(testResults).every(Boolean);
            if (allPassed) {
                logResult('🎉 جميع الاختبارات نجحت! الإصلاح جاهز للنشر.');
            } else {
                logResult('⚠️ بعض الاختبارات فشلت. يرجى مراجعة النتائج.');
            }
        }
        
        // تشغيل اختبار أساسي عند التحميل
        window.addEventListener('load', () => {
            logResult('📄 تم تحميل صفحة الاختبار النهائي');
            logResult('💡 اضغط على "تشغيل جميع الاختبارات" للبدء');
        });
    </script>
</body>
</html>
