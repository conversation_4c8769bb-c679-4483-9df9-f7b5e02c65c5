// Cloudflare Pages Function للتعامل مع طلبات API
// API Handler for Cloudflare Pages

export async function onRequest(context) {
    const { request, env } = context;
    const url = new URL(request.url);
    const path = url.pathname;

    // إعداد CORS headers
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // التعامل مع طلبات OPTIONS (CORS preflight)
    if (request.method === 'OPTIONS') {
        return new Response(null, { headers: corsHeaders });
    }

    try {
        // إعدادات Supabase
        const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
        const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************._BQpMA9YZpXCjvpoNRK2QdoecsE5VQsr3AN2DJhj2rw";

        // معالجة المسارات المختلفة
        if (path.startsWith('/api/health')) {
            return await handleHealth(request, corsHeaders);
        } else if (path.startsWith('/api/mod/')) {
            return await handleModData(request, corsHeaders, SUPABASE_URL, SUPABASE_KEY);
        } else if (path.startsWith('/api/download/')) {
            return await handleDownload(request, corsHeaders, SUPABASE_URL, SUPABASE_KEY);
        } else if (path.startsWith('/api/ads/')) {
            return await handleAdsSettings(request, corsHeaders, SUPABASE_URL, SUPABASE_KEY);
        } else if (path.startsWith('/api/customization/')) {
            return await handleCustomization(request, corsHeaders, SUPABASE_URL, SUPABASE_KEY);
        } else if (path.startsWith('/api/webhook')) {
            return await handleWebhook(request, corsHeaders);
        }

        // مسار غير معروف
        return new Response(JSON.stringify({
            error: 'مسار غير معروف',
            path: path,
            available_endpoints: [
                '/api/health',
                '/api/mod/{id}',
                '/api/download/{id}',
                '/api/ads/{user_id}',
                '/api/customization/{user_id}',
                '/api/webhook'
            ]
        }), {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('خطأ في معالجة الطلب:', error);
        return new Response(JSON.stringify({
            error: 'خطأ داخلي في الخادم',
            message: error.message
        }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}

// معالج فحص الصحة
async function handleHealth(request, corsHeaders) {
    return new Response(JSON.stringify({
        status: "healthy",
        timestamp: new Date().toISOString(),
        service: "Modetaris Bot API",
        version: "2.0.0",
        hosting: "Cloudflare Pages",
        endpoints: {
            health: "/api/health",
            mod: "/api/mod/{id}",
            download: "/api/download/{id}",
            ads: "/api/ads/{user_id}",
            customization: "/api/customization/{user_id}",
            webhook: "/api/webhook"
        }
    }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
}

// معالج بيانات المود
async function handleModData(request, corsHeaders, supabaseUrl, supabaseKey) {
    const url = new URL(request.url);
    const modId = url.pathname.split('/').pop();

    try {
        const response = await fetch(`${supabaseUrl}/rest/v1/minemods?id=eq.${modId}&select=*`, {
            headers: {
                'apikey': supabaseKey,
                'Authorization': `Bearer ${supabaseKey}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب بيانات المود');
        }

        const mods = await response.json();
        if (mods.length === 0) {
            return new Response(JSON.stringify({
                error: 'المود غير موجود',
                mod_id: modId
            }), {
                status: 404,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        return new Response(JSON.stringify({
            success: true,
            data: mods[0]
        }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('خطأ في جلب بيانات المود:', error);
        return new Response(JSON.stringify({
            error: 'خطأ في جلب بيانات المود',
            message: error.message
        }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}

// معالج التحميل
async function handleDownload(request, corsHeaders, supabaseUrl, supabaseKey) {
    const url = new URL(request.url);
    const modId = url.pathname.split('/').pop();

    try {
        // جلب بيانات المود
        const response = await fetch(`${supabaseUrl}/rest/v1/minemods?id=eq.${modId}&select=*`, {
            headers: {
                'apikey': supabaseKey,
                'Authorization': `Bearer ${supabaseKey}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب بيانات المود');
        }

        const mods = await response.json();
        if (mods.length === 0) {
            return new Response('المود غير موجود', { 
                status: 404,
                headers: corsHeaders
            });
        }

        const mod = mods[0];
        
        // إعادة توجيه لرابط التحميل
        if (mod.download_url) {
            return Response.redirect(mod.download_url, 302);
        } else {
            return new Response('رابط التحميل غير متاح', { 
                status: 404,
                headers: corsHeaders
            });
        }

    } catch (error) {
        console.error('خطأ في تحميل المود:', error);
        return new Response('خطأ في تحميل المود', { 
            status: 500,
            headers: corsHeaders
        });
    }
}

// معالج إعدادات الإعلانات
async function handleAdsSettings(request, corsHeaders, supabaseUrl, supabaseKey) {
    const url = new URL(request.url);
    const userId = url.pathname.split('/').pop();

    try {
        const response = await fetch(`${supabaseUrl}/rest/v1/user_ads_settings?user_id=eq.${userId}`, {
            headers: {
                'apikey': supabaseKey,
                'Authorization': `Bearer ${supabaseKey}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب إعدادات الإعلانات');
        }

        const data = await response.json();
        return new Response(JSON.stringify({
            success: true,
            data: data.length > 0 ? data[0] : null
        }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('خطأ في جلب إعدادات الإعلانات:', error);
        return new Response(JSON.stringify({
            error: 'خطأ في جلب إعدادات الإعلانات',
            message: error.message
        }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}

// معالج إعدادات التخصيص
async function handleCustomization(request, corsHeaders, supabaseUrl, supabaseKey) {
    const url = new URL(request.url);
    const userId = url.pathname.split('/').pop();

    try {
        const response = await fetch(`${supabaseUrl}/rest/v1/page_customization_settings?user_id=eq.${userId}`, {
            headers: {
                'apikey': supabaseKey,
                'Authorization': `Bearer ${supabaseKey}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب إعدادات التخصيص');
        }

        const data = await response.json();
        return new Response(JSON.stringify({
            success: true,
            data: data.length > 0 ? data[0] : null
        }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('خطأ في جلب إعدادات التخصيص:', error);
        return new Response(JSON.stringify({
            error: 'خطأ في جلب إعدادات التخصيص',
            message: error.message
        }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}

// معالج webhook للبوت
async function handleWebhook(request, corsHeaders) {
    if (request.method !== 'POST') {
        return new Response('Method not allowed', { 
            status: 405,
            headers: corsHeaders
        });
    }

    try {
        const update = await request.json();
        
        // هنا يمكن إضافة منطق معالجة updates البوت
        console.log('Received webhook update:', update);

        return new Response(JSON.stringify({ 
            ok: true,
            message: 'Webhook received successfully'
        }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    } catch (error) {
        console.error('خطأ في معالجة webhook:', error);
        return new Response(JSON.stringify({ 
            error: error.message 
        }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}
